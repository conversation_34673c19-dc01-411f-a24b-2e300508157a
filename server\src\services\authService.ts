import jwt from 'jsonwebtoken'
import bcrypt from 'bcryptjs'
import { PrismaClient } from '@prisma/client'
import { ApiResponse } from '@shared/types'
import { AppError, AuthenticationError, ValidationError } from '../utils/AppError'
import { logger, authLogger } from '../utils/logger'

const prisma = new PrismaClient()

interface TokenPayload {
  userId: string
  username: string
  role: string
  companyId: string
  branchId: string
}

interface LoginRequest {
  username: string
  password: string
}

interface LoginResponse {
  user: {
    id: string
    username: string
    firstName: string
    lastName: string
    email?: string
    role: string
    companyId: string
    branchId: string
  }
  token: string
}

export class AuthService {
  private readonly JWT_SECRET = process.env.JWT_SECRET || 'your-secret-key'
  private readonly JWT_EXPIRE = process.env.JWT_EXPIRE || '12h' // Vardiya süresi

  /**
   * <PERSON><PERSON><PERSON><PERSON><PERSON> girişi
   */
  async login(credentials: LoginRequest, ip?: string, userAgent?: string): Promise<ApiResponse<LoginResponse>> {
    try {
      // Log login attempt
      logger.debug('Login attempt started', { username: credentials.username, ip, userAgent })
      authLogger.loginAttempt(credentials.username, ip || 'unknown', userAgent)

      // Kullanıcıyı bul
      const user = await prisma.user.findUnique({
        where: {
          username: credentials.username,
          active: true
        },
        include: {
          company: true,
          branch: true
        }
      })

      if (!user) {
        authLogger.loginFailure(credentials.username, 'User not found', ip || 'unknown')
        throw new AuthenticationError('Kullanıcı adı veya şifre hatalı')
      }

      // Şifre kontrolü
      const isPasswordValid = await bcrypt.compare(credentials.password, user.password)
      if (!isPasswordValid) {
        authLogger.loginFailure(credentials.username, 'Invalid password', ip || 'unknown')
        throw new AuthenticationError('Kullanıcı adı veya şifre hatalı')
      }

      // Token oluştur
      const tokenPayload: TokenPayload = {
        userId: user.id,
        username: user.username,
        role: user.role,
        companyId: user.companyId,
        branchId: user.branchId || '' // null kontrolü
      }

      const token = this.generateToken(tokenPayload)

      // Son giriş zamanını güncelle
      await prisma.user.update({
        where: { id: user.id },
        data: {
          lastLoginAt: new Date()
        }
      })

      // Log successful login
      authLogger.loginSuccess(user.id, user.username, ip || 'unknown')

      const response: LoginResponse = {
        user: {
          id: user.id,
          username: user.username,
          firstName: user.firstName,
          lastName: user.lastName,
          email: user.email || undefined,
          role: user.role,
          companyId: user.companyId,
          branchId: user.branchId || ''
        },
        token
      }

      return {
        success: true,
        data: response,
        message: 'Giriş başarılı'
      }
    } catch (error) {
      logger.error('Login error:', { error: error instanceof Error ? error.message : error, username: credentials.username })

      if (error instanceof AppError) {
        return {
          success: false,
          error: error.message
        }
      }

      throw new AppError('Giriş sırasında bir hata oluştu', 500, 'LOGIN_ERROR')
    }
  }

  /**
   * Token doğrulama
   */
  verifyToken(token: string): TokenPayload | null {
    try {
      const decoded = jwt.verify(token, this.JWT_SECRET) as TokenPayload
      return decoded
    } catch (error) {
      if (error instanceof jwt.TokenExpiredError) {
        authLogger.tokenExpired(token, 'Token expired')
      }
      logger.debug('Token verification failed:', { error: error instanceof Error ? error.message : error })
      return null
    }
  }

  /**
   * Çıkış (Basit - sadece log)
   */
  async logout(userId: string, username?: string): Promise<ApiResponse<null>> {
    try {
      // Kullanıcı bilgisini al
      const user = await prisma.user.findUnique({
        where: { id: userId },
        select: { username: true }
      })

      // Log logout
      authLogger.logout(userId, username || user?.username || 'unknown')

      // Son aktivite zamanını güncelle
      await prisma.user.update({
        where: { id: userId },
        data: { lastLoginAt: new Date() }
      })

      return {
        success: true,
        message: 'Çıkış başarılı'
      }
    } catch (error) {
      logger.error('Logout error:', { error: error instanceof Error ? error.message : error, userId })
      throw new AppError('Çıkış sırasında bir hata oluştu', 500, 'LOGOUT_ERROR')
    }
  }

  /**
   * Token oluştur
   */
  private generateToken(payload: TokenPayload): string {
    return jwt.sign(payload, this.JWT_SECRET, { expiresIn: this.JWT_EXPIRE as string })
  }
}

export const authService = new AuthService()
