import { Router } from 'express'
import {
  login,
  logout,
  getProfile,
  changePassword
} from '../controllers/authController'
import { authenticateToken } from '../middlewares/authMiddleware'
import { validate, loginSchema, changePasswordSchema } from '../validators/authValidator'
import { asyncHandler } from '../middlewares/errorMiddleware'
import rateLimit from 'express-rate-limit'

const router = Router()

// Rate limiting for auth endpoints
const authLimiter = rateLimit({
  windowMs: 15 * 60 * 1000, // 15 dakika
  max: 5, // 15 dakikada maksimum 5 deneme
  message: {
    success: false,
    error: 'Çok fazla giriş denemesi. 15 dakika sonra tekrar deneyin.'
  },
  standardHeaders: true,
  legacyHeaders: false,
})

const passwordChangeLimiter = rateLimit({
  windowMs: 60 * 60 * 1000, // 1 saat
  max: 3, // 1 saatte maksimum 3 şifre değ<PERSON>ştirme
  message: {
    success: false,
    error: 'Çok fazla şifre değiştirme denemesi. 1 saat sonra tekrar deneyin.'
  }
})

/**
 * @route   POST /api/auth/login
 * @desc    Kullanıcı girişi
 * @access  Public
 */
router.post('/login', authLimiter, validate(loginSchema), asyncHandler(login))

// Refresh token artık gerekli değil - basit token sistemi

/**
 * @route   POST /api/auth/logout
 * @desc    Kullanıcı çıkışı
 * @access  Private
 */
router.post('/logout', authenticateToken, asyncHandler(logout))

/**
 * @route   GET /api/auth/profile
 * @desc    Kullanıcı profili
 * @access  Private
 */
router.get('/profile', authenticateToken, asyncHandler(getProfile))

/**
 * @route   PUT /api/auth/change-password
 * @desc    Şifre değiştirme
 * @access  Private
 */
router.put('/change-password', authenticateToken, passwordChangeLimiter, validate(changePasswordSchema), asyncHandler(changePassword))

export default router
