import { Request, Response, NextFunction } from 'express'
import { authService } from '../services/authService'
import { PrismaClient } from '@prisma/client'
import { AppError, ValidationError } from '../utils/AppError'
import { logger, authLogger } from '../utils/logger'

const prisma = new PrismaClient()

/**
 * Kullanıcı girişi
 */
export const login = async (req: Request, res: Response, next: NextFunction) => {
  try {
    const { username, password } = req.body
    const ip = req.ip || req.connection.remoteAddress || 'unknown'
    const userAgent = req.get('User-Agent')

    // Validation artık middleware'de yapılıyor
    const result = await authService.login({ username, password }, ip, userAgent)

    if (!result.success) {
      return res.status(401).json({
        success: false,
        error: result.error,
        timestamp: new Date().toISOString(),
        path: req.path
      })
    }

    res.json(result)
  } catch (error) {
    next(error) // Error middleware'e gönder
  }
}

// Refresh token artık gerekli değil - basit token sistemi

/**
 * Kullanıcı çıkışı
 */
export const logout = async (req: Request, res: Response, next: NextFunction) => {
  try {
    const userId = req.user?.userId
    const username = req.user?.username

    if (!userId) {
      throw new ValidationError('Kullanıcı kimliği bulunamadı')
    }

    const result = await authService.logout(userId, username)
    res.json(result)
  } catch (error) {
    next(error)
  }
}

/**
 * Kullanıcı profili
 */
export const getProfile = async (req: Request, res: Response, next: NextFunction) => {
  try {
    const userId = req.user?.userId

    if (!userId) {
      throw new ValidationError('Kullanıcı kimliği bulunamadı')
    }

    const user = await prisma.user.findUnique({
      where: { id: userId },
      select: {
        id: true,
        username: true,
        firstName: true,
        lastName: true,
        email: true,
        role: true,
        active: true,
        createdAt: true,
        lastLoginAt: true,
        company: {
          select: {
            id: true,
            name: true,
            logo: true
          }
        },
        branch: {
          select: {
            id: true,
            name: true,
            code: true
          }
        }
      }
    })

    if (!user) {
      throw new AppError('Kullanıcı bulunamadı', 404, 'USER_NOT_FOUND')
    }

    res.json({
      success: true,
      data: user
    })
  } catch (error) {
    next(error)
  }
}

/**
 * Şifre değiştirme
 */
export const changePassword = async (req: Request, res: Response, next: NextFunction) => {
  try {
    const userId = req.user?.userId
    const { currentPassword, newPassword } = req.body

    if (!userId) {
      throw new ValidationError('Kullanıcı kimliği bulunamadı')
    }

    // Validation artık middleware'de yapılıyor
    const user = await prisma.user.findUnique({
      where: { id: userId }
    })

    if (!user) {
      throw new AppError('Kullanıcı bulunamadı', 404, 'USER_NOT_FOUND')
    }

    // Mevcut şifre kontrolü
    const bcrypt = require('bcryptjs')
    const isCurrentPasswordValid = await bcrypt.compare(currentPassword, user.password)

    if (!isCurrentPasswordValid) {
      throw new ValidationError('Mevcut şifre hatalı')
    }

    // Yeni şifreyi hash'le
    const hashedNewPassword = await bcrypt.hash(newPassword, 10)

    // Şifreyi güncelle
    await prisma.user.update({
      where: { id: userId },
      data: {
        password: hashedNewPassword
      }
    })

    // Log password change
    authLogger.passwordChange(userId, user.username)

    res.json({
      success: true,
      message: 'Şifre başarıyla değiştirildi. Lütfen tekrar giriş yapın.'
    })
  } catch (error) {
    next(error)
  }
}
