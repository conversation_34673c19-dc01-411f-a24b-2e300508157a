import React from 'react'
import { Box, Container, Paper, Typography, useTheme } from '@mui/material'
import { useTranslation } from 'react-i18next'
import { keyframes } from '@emotion/react'

// Floating animation for background elements
const float = keyframes`
  0% { transform: translateY(0px) rotate(0deg); }
  33% { transform: translateY(-10px) rotate(1deg); }
  66% { transform: translateY(5px) rotate(-1deg); }
  100% { transform: translateY(0px) rotate(0deg); }
`

const floatSlow = keyframes`
  0% { transform: translateY(0px) rotate(0deg); }
  50% { transform: translateY(-15px) rotate(2deg); }
  100% { transform: translateY(0px) rotate(0deg); }
`

interface AuthLayoutProps {
  children: React.ReactNode
}

export const AuthLayout: React.FC<AuthLayoutProps> = ({ children }) => {
  const theme = useTheme()
  const { t } = useTranslation()

  return (
    <Box
      sx={{
        minHeight: '100vh',
        display: 'flex',
        backgroundColor: theme.palette.background.default,
        position: 'relative',
        overflow: 'hidden',
      }}
    >
      {/* Animated Background Elements */}
      <Box
        sx={{
          position: 'absolute',
          top: '10%',
          left: '5%',
          width: 60,
          height: 60,
          borderRadius: '50%',
          backgroundColor: theme.palette.primary.main,
          opacity: 0.1,
          animation: `${float} 6s ease-in-out infinite`,
        }}
      />
      <Box
        sx={{
          position: 'absolute',
          top: '60%',
          right: '10%',
          width: 40,
          height: 40,
          borderRadius: '50%',
          backgroundColor: theme.palette.secondary.main,
          opacity: 0.1,
          animation: `${floatSlow} 8s ease-in-out infinite`,
        }}
      />
      <Box
        sx={{
          position: 'absolute',
          bottom: '20%',
          left: '15%',
          width: 80,
          height: 80,
          borderRadius: '50%',
          backgroundColor: theme.palette.success.main,
          opacity: 0.08,
          animation: `${float} 7s ease-in-out infinite`,
        }}
      />

      <Container maxWidth="lg" sx={{ display: 'flex', alignItems: 'center', py: 4 }}>
        <Paper
          elevation={8}
          sx={{
            display: 'flex',
            width: '100%',
            maxWidth: 1000,
            minHeight: 600,
            mx: 'auto',
            borderRadius: 3,
            overflow: 'hidden',
            boxShadow: '0 20px 40px rgba(0,0,0,0.1)',
          }}
        >
          {/* Left Side - Hero Section */}
          <Box
            sx={{
              flex: 1,
              background: `linear-gradient(135deg, ${theme.palette.primary.main} 0%, ${theme.palette.primary.dark} 100%)`,
              color: 'white',
              p: 6,
              display: 'flex',
              flexDirection: 'column',
              justifyContent: 'center',
              position: 'relative',
              minHeight: 600,
            }}
          >
            {/* Background Pattern */}
            <Box
              sx={{
                position: 'absolute',
                top: 0,
                left: 0,
                right: 0,
                bottom: 0,
                backgroundImage: `url("data:image/svg+xml,%3Csvg width='60' height='60' viewBox='0 0 60 60' xmlns='http://www.w3.org/2000/svg'%3E%3Cg fill='none' fill-rule='evenodd'%3E%3Cg fill='%23ffffff' fill-opacity='0.1'%3E%3Ccircle cx='30' cy='30' r='2'/%3E%3C/g%3E%3C/g%3E%3C/svg%3E")`,
                opacity: 0.3,
              }}
            />

            {/* Content */}
            <Box sx={{ position: 'relative', zIndex: 1 }}>
              {/* Logo/Icon */}


              {/* Testimonial */}
              <Box
                sx={{
                  p: 3,
                  borderRadius: 2,
                  backgroundColor: 'rgba(255,255,255,0.1)',
                  backdropFilter: 'blur(10px)',
                  border: '1px solid rgba(255,255,255,0.2)',
                }}
              >
                <Typography
                  variant="body1"
                  sx={{
                    fontStyle: 'italic',
                    mb: 2,
                    lineHeight: 1.6,
                  }}
                >
                  "{t('auth.hero.testimonial')}"
                </Typography>
                <Box>
                  <Typography variant="subtitle2" sx={{ fontWeight: 'bold' }}>
                    {t('auth.hero.author')}
                  </Typography>
                  <Typography variant="caption" sx={{ opacity: 0.8 }}>
                    {t('auth.hero.position')}
                  </Typography>
                </Box>
              </Box>
            </Box>
          </Box>

          {/* Right Side - Form Section */}
          <Box
            sx={{
              flex: 1,
              p: 6,
              display: 'flex',
              flexDirection: 'column',
              justifyContent: 'center',
              backgroundColor: theme.palette.background.paper,
              minHeight: 600,
            }}
          >
            {children}
          </Box>
        </Paper>
      </Container>
    </Box>
  )
}
